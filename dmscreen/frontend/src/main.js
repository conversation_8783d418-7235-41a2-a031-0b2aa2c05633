import './style.css';

// HUD Application Logic
class DMScreenHUD {
    constructor() {
        this.messages = [];
        this.logText = '';
        this.messageList = document.getElementById('message-list');
        this.statusElement = document.getElementById('session-status');
        this.clearBtn = document.getElementById('clear-btn');
        this.settingsBtn = document.getElementById('settings-btn');
        this.textCanvas = document.getElementById('text-canvas');
        this.clearLogBtn = document.getElementById('clear-log-btn');
        this.commandInput = document.getElementById('command-input');
        this.executeBtn = document.getElementById('execute-btn');
        this.startSessionBtn = document.getElementById('start-session-btn');
        this.stopSessionBtn = document.getElementById('stop-session-btn');
        this.textCanvasUpdateInterval = null;

        // Preferences modal elements
        this.preferencesModal = document.getElementById('preferences-modal');
        this.preferencesForm = document.getElementById('preferences-form');
        this.preferencesClose = document.getElementById('preferences-close');
        this.preferencesSave = document.getElementById('preferences-save');
        this.preferencesCancel = document.getElementById('preferences-cancel');
        this.dndVersionRadios = document.querySelectorAll('input[name="dnd-version"]');
        this.geminiApiKeyInput = document.getElementById('gemini-api-key');
        this.assemblyAIApiKeyInput = document.getElementById('assemblyai-api-key');

        // Debug console elements
        this.debugConsoleBtn = document.getElementById('debug-console-btn');
        this.debugConsoleModal = document.getElementById('debug-console-modal');
        this.debugConsoleClose = document.getElementById('debug-console-close');
        this.debugConsoleContent = document.getElementById('debug-console-content');
        this.debugClearBtn = document.getElementById('debug-clear-btn');
        this.debugCopyBtn = document.getElementById('debug-copy-btn');
        this.debugExportBtn = document.getElementById('debug-export-btn');
        this.debugMessageCount = document.getElementById('debug-message-count');
        this.debugAutoScroll = document.getElementById('debug-auto-scroll');
        this.debugFilterLevel = document.getElementById('debug-filter-level');

        this.debugMessages = [];
        this.debugMessageId = 0;

        this.sessionActive = false;
        this.lastContentLength = 0;

        // Timer elements and state
        this.timerDisplay = document.getElementById('timer-display');
        this.timerToggle = document.getElementById('timer-toggle');
        this.sessionStartTime = null;
        this.timerInterval = null;
        this.showElapsed = true; // true = elapsed, false = remaining
        this.sessionDuration = 4 * 60 * 60 * 1000; // 4 hours in milliseconds

        this.initializeEventListeners();
        this.updateStatus('Ready');

        // Add some sample messages for testing
        this.addSampleMessages();
        this.addSampleLogText();

        // No polling needed - file tailing handles updates
        // We'll only refresh when sessions start/stop
    }

    initializeEventListeners() {
        this.clearBtn.addEventListener('click', () => this.clearMessages());
        this.debugConsoleBtn.addEventListener('click', () => this.showDebugConsole());
        this.settingsBtn.addEventListener('click', () => this.showSettings());
        this.clearLogBtn.addEventListener('click', () => this.clearLog());
        this.executeBtn.addEventListener('click', () => this.executeCommand());
        this.startSessionBtn.addEventListener('click', () => this.startSession());
        this.stopSessionBtn.addEventListener('click', () => this.stopSession());
        this.timerToggle.addEventListener('click', () => this.toggleTimerMode());

        // Debug console event listeners
        this.debugConsoleClose.addEventListener('click', () => this.closeDebugConsole());
        this.debugClearBtn.addEventListener('click', () => this.clearDebugConsole());
        this.debugCopyBtn.addEventListener('click', () => this.copyDebugConsole());
        this.debugExportBtn.addEventListener('click', () => this.exportDebugConsole());
        this.debugFilterLevel.addEventListener('change', () => this.filterDebugMessages());

        // Close debug console when clicking outside
        this.debugConsoleModal.addEventListener('click', (e) => {
            if (e.target === this.debugConsoleModal) {
                this.closeDebugConsole();
            }
        });

        // Preferences modal event listeners
        this.preferencesClose.addEventListener('click', (e) => {
            e.preventDefault();
            this.closePreferences();
        });
        this.preferencesSave.addEventListener('click', (e) => {
            e.preventDefault();
            this.savePreferences();
        });
        this.preferencesCancel.addEventListener('click', (e) => {
            e.preventDefault();
            this.closePreferences();
        });

        // Close modal when clicking outside
        this.preferencesModal.addEventListener('click', (e) => {
            if (e.target === this.preferencesModal) {
                this.closePreferences();
            }
        });

        // Handle API key masking on blur
        this.geminiApiKeyInput.addEventListener('blur', () => this.maskApiKeyInput(this.geminiApiKeyInput));
        this.assemblyAIApiKeyInput.addEventListener('blur', () => this.maskApiKeyInput(this.assemblyAIApiKeyInput));

        // Prevent form submission
        this.preferencesForm.addEventListener('submit', (e) => {
            e.preventDefault();
            return false;
        });

        // Handle Enter key in command input
        this.commandInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                this.executeCommand();
            }
        });
    }

    addMessage(type, content, imagePath = null) {
        const message = {
            id: Date.now(),
            type: type,
            content: content,
            imagePath: imagePath,
            timestamp: new Date()
        };

        this.messages.unshift(message); // Add to beginning
        this.renderMessage(message);
        this.updateStatus(`${this.messages.length} messages`);
    }

    renderMessage(message) {
        const messageElement = document.createElement('div');
        messageElement.className = 'message-item';
        messageElement.dataset.messageId = message.id;

        messageElement.innerHTML = `
            <div class="message-header">
                <span class="message-type">${message.type}</span>
                <span class="message-time">${this.formatTime(message.timestamp)}</span>
            </div>
            <div class="message-content">${message.content}</div>
            <div class="message-actions">
                <button class="action-btn gallery" onclick="hud.sendToGallery(${message.id})">📸 Gallery</button>
                <button class="action-btn log" onclick="hud.logMessage(${message.id})">📝 Log</button>
            </div>
        `;

        // Insert at the beginning of the message list
        this.messageList.insertBefore(messageElement, this.messageList.firstChild);
    }

    formatTime(date) {
        return date.toLocaleTimeString('en-US', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    clearMessages() {
        this.messages = [];
        this.messageList.innerHTML = '';
        this.updateStatus('Messages cleared');
    }

    sendToGallery(messageId) {
        const message = this.messages.find(m => m.id === messageId);
        if (message) {
            console.log('Sending to gallery:', message);
            this.updateStatus(`Sent "${message.type}" to gallery`);
            // TODO: Implement gallery integration
        }
    }

    logMessage(messageId) {
        const message = this.messages.find(m => m.id === messageId);
        if (message) {
            console.log('Logging message:', message);
            this.updateStatus(`Logged "${message.type}" message`);
            // TODO: Implement logging functionality
        }
    }

    async showSettings() {
        try {
            // Load current settings from backend
            const settings = await window.go.main.App.GetSettings();
            this.populatePreferencesForm(settings);
            this.preferencesModal.classList.remove('hidden');
        } catch (error) {
            console.error('Failed to load settings:', error);
            this.updateStatus('Failed to load settings');
        }
    }

    closePreferences() {
        this.preferencesModal.classList.add('hidden');
    }

    async savePreferences() {
        try {
            const settings = this.getPreferencesFromForm();
            const result = await window.go.main.App.SaveSettings(settings);

            if (result.includes('successfully')) {
                this.updateStatus('Settings saved');
                this.closePreferences();
            } else {
                this.updateStatus(result);
            }
        } catch (error) {
            console.error('Failed to save settings:', error);
            this.updateStatus('Failed to save settings');
        }
    }

    populatePreferencesForm(settings) {
        // Set D&D version radio button
        this.dndVersionRadios.forEach(radio => {
            radio.checked = radio.value === settings.dndVersion;
        });

        // Set Gemini API key (will be masked if it exists)
        this.geminiApiKeyInput.value = settings.geminiAPIKey || '';
        this.geminiApiKeyInput.dataset.isMasked = settings.geminiAPIKey && settings.geminiAPIKey.includes('***') ? 'true' : 'false';
        this.geminiApiKeyInput.dataset.hasValue = settings.geminiAPIKey ? 'true' : 'false';

        // Set AssemblyAI API key (will be masked if it exists)
        this.assemblyAIApiKeyInput.value = settings.assemblyAIKey || '';
        this.assemblyAIApiKeyInput.dataset.isMasked = settings.assemblyAIKey && settings.assemblyAIKey.includes('***') ? 'true' : 'false';
        this.assemblyAIApiKeyInput.dataset.hasValue = settings.assemblyAIKey ? 'true' : 'false';
    }

    getPreferencesFromForm() {
        // Get selected D&D version
        const selectedVersion = document.querySelector('input[name="dnd-version"]:checked');
        const dndVersion = selectedVersion ? selectedVersion.value : '2024';

        // Get Gemini API key
        let geminiAPIKey = this.geminiApiKeyInput.value.trim();

        // Get AssemblyAI API key
        let assemblyAIKey = this.assemblyAIApiKeyInput.value.trim();

        // If the fields are masked and haven't been changed, send the masked values
        // The backend will handle keeping the original values

        return {
            dndVersion: dndVersion,
            geminiAPIKey: geminiAPIKey,
            assemblyAIKey: assemblyAIKey
        };
    }

    maskApiKeyInput(inputElement) {
        const value = inputElement.value.trim();
        if (value && !value.includes('*') && value.length > 7) {
            // Only mask if it's a new value that's not already masked
            // Preserve length by filling middle with appropriate number of asterisks
            const middleLength = value.length - 7; // Total length minus first 3 and last 4
            const masked = value.substring(0, 3) + '*'.repeat(middleLength) + value.substring(value.length - 4);
            inputElement.value = masked;
            inputElement.dataset.originalValue = value; // Store original for saving
        } else if (value && !value.includes('*') && value.length <= 7) {
            // For short keys, mask the entire length
            const masked = '*'.repeat(value.length);
            inputElement.value = masked;
            inputElement.dataset.originalValue = value; // Store original for saving
        }
    }

    // Text Canvas Methods
    appendToLog(text) {
        this.logText += text + '\n';
        this.textCanvas.textContent = this.logText;
        // Auto-scroll to bottom
        this.textCanvas.scrollTop = this.textCanvas.scrollHeight;
    }

    async clearLog() {
        try {
            // Clear the backend buffer
            await window.go.main.App.ClearTextCanvas();
            // Clear the frontend display
            this.logText = '';
            this.textCanvas.textContent = '';
            this.updateStatus('Log cleared');
        } catch (error) {
            console.error('Failed to clear log:', error);
            this.updateStatus('Clear failed');
        }
    }

    // Periodic refresh (file tailing handles real-time updates)
    startPeriodicRefresh() {
        // Only refresh when session is active, and much less frequently
        if (!this.sessionActive) return;

        const refreshInterval = 10000; // 10 seconds - just for safety sync

        this.textCanvasUpdateInterval = setInterval(async () => {
            if (!this.sessionActive) {
                this.stopPeriodicRefresh();
                return;
            }

            await this.refreshTextCanvas();
        }, refreshInterval);
    }

    stopPeriodicRefresh() {
        if (this.textCanvasUpdateInterval) {
            clearInterval(this.textCanvasUpdateInterval);
            this.textCanvasUpdateInterval = null;
        }
    }

    // Command Area Methods
    executeCommand() {
        const command = this.commandInput.value.trim();
        if (!command) return;

        this.updateStatus(`Executing: ${command}`);

        // Add command to log
        this.appendToLog(`> ${command}`);

        // Process command
        this.processCommand(command);

        // Clear input
        this.commandInput.value = '';
    }

    async processCommand(command) {
        // Simple command processing - can be expanded
        const lowerCommand = command.toLowerCase();

        if (lowerCommand.startsWith('search ')) {
            const searchTerm = command.substring(7);
            await this.sendToLocalscribeMetadata(`search command: "${searchTerm}"`);
            // TODO: Implement search functionality
        } else if (lowerCommand === 'clear') {
            this.clearMessages();
            await this.sendToLocalscribeMetadata('messages cleared via command');
        } else if (lowerCommand === 'stats') {
            try {
                const stats = await window.go.main.App.GetTextCanvasStats();
                await this.sendToLocalscribeMetadata(`buffer stats: ${stats.lines} lines, ${stats.sizeKB.toFixed(1)} KB`);
            } catch (error) {
                await this.sendToLocalscribeMetadata('failed to get buffer stats');
            }
        } else if (lowerCommand === 'help') {
            await this.sendToLocalscribeMetadata('help requested - available commands: search, clear, stats, help');
        } else {
            await this.sendToLocalscribeMetadata(`unknown command: "${command}"`);
        }
    }

    // Helper method to send command output via localscribe REST API
    async sendToLocalscribeMetadata(text) {
        try {
            // Use localscribe's REST API to add metadata
            const response = await fetch('http://localhost:8080/metadata?body=' + encodeURIComponent(text));
            if (!response.ok) {
                console.error('Failed to send metadata to localscribe:', response.statusText);
            }
        } catch (error) {
            console.error('Failed to send metadata to localscribe:', error);
            // Fallback: show in status if localscribe isn't running
            this.updateStatus(`Command: ${text}`);
        }
    }

    // Session Control Methods
    async startSession() {
        this.updateStatus('Starting session...');
        this.startSessionBtn.disabled = true;

        try {
            // Call the Go backend to start localscribe library
            const result = await window.go.main.App.StartLocalscribeLibrary();
            console.log(`Session started: ${result}`);
            this.addDebugMessage('dmscreen', `Session started: ${result}`, 'info');
            this.updateStatus('Session active');
            this.startSessionBtn.disabled = true;
            this.stopSessionBtn.disabled = false;

            // Update session state and start periodic refresh
            this.sessionActive = true;

            // Start timer and periodic refresh
            this.startTimer();
            this.startPeriodicRefresh();
            setTimeout(() => this.refreshTextCanvas(), 2000); // Wait for log file to be created
        } catch (error) {
            console.error(`Failed to start session: ${error}`);
            this.addDebugMessage('dmscreen', `Failed to start session: ${error}`, 'error');
            this.updateStatus('Start failed');
            this.startSessionBtn.disabled = false;
        }
    }

    // Manual refresh method
    async refreshTextCanvas() {
        try {
            const currentLineCount = await window.go.main.App.GetTextCanvasLineCount();

            if (currentLineCount > this.lastContentLength) {
                const newContent = await window.go.main.App.GetTextCanvasContentSince(this.lastContentLength);

                if (newContent && newContent.length > 0) {
                    if (this.logText) {
                        this.logText += '\n' + newContent.join('\n');
                    } else {
                        this.logText = newContent.join('\n');
                    }

                    this.textCanvas.textContent = this.logText;
                    this.lastContentLength = currentLineCount;
                    this.textCanvas.scrollTop = this.textCanvas.scrollHeight;
                }
            }
        } catch (error) {
            console.debug('Manual refresh error:', error);
        }
    }

    async stopSession() {
        this.updateStatus('Stopping session...');
        this.stopSessionBtn.disabled = true;

        try {
            // Call the Go backend to stop localscribe library
            const result = await window.go.main.App.StopLocalscribeLibrary();
            console.log(`Session stopped: ${result}`);
            this.addDebugMessage('dmscreen', `Session stopped: ${result}`, 'info');
            this.updateStatus('Ready');
            this.startSessionBtn.disabled = false;
            this.stopSessionBtn.disabled = true;

            // Update session state and stop timer/refresh
            this.sessionActive = false;
            this.stopTimer();
            this.stopPeriodicRefresh();
        } catch (error) {
            console.error(`Failed to stop session: ${error}`);
            this.addDebugMessage('dmscreen', `Failed to stop session: ${error}`, 'error');
            this.updateStatus('Stop failed');
            this.stopSessionBtn.disabled = false;
        }
    }

// Legacy embedded binary testing removed - now using integrated library approach

    updateStatus(text) {
        this.statusElement.textContent = text;
        setTimeout(() => {
            this.statusElement.textContent = 'Ready';
        }, 3000);
    }

    addSampleMessages() {
        // Add some sample messages for testing
        setTimeout(() => {
            this.addMessage('NPC', 'Bartender Gareth: "Welcome to the Prancing Pony, travelers!"');
        }, 1000);

        setTimeout(() => {
            this.addMessage('LORE', 'The ancient ruins of Shadowmere are said to contain powerful artifacts.');
        }, 2000);

        setTimeout(() => {
            this.addMessage('DC', 'Perception DC 15 to notice the hidden door behind the tapestry.');
        }, 3000);
    }

    addSampleLogText() {
        // Add some sample log text for testing
        setTimeout(() => {
            // Add some sample debug messages
            this.addDebugMessage('dmscreen', 'Application initialized successfully', 'info');
            this.addDebugMessage('localscribe', 'Transcription service ready', 'info');
            this.addDebugMessage('system', 'Debug console activated', 'debug');
        }, 500);
    }

    // Timer Methods
    startTimer() {
        this.resetTimer(); // Clear previous session time
        this.sessionStartTime = Date.now();
        this.timerInterval = setInterval(() => this.updateTimer(), 1000);
        this.updateTimer(); // Update immediately
    }

    stopTimer() {
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
            this.timerInterval = null;
        }
        // Don't clear sessionStartTime or display - preserve the final time
        // Timer will be reset when startTimer() is called again
    }

    resetTimer() {
        this.sessionStartTime = null;
        this.timerDisplay.textContent = '00:00:00';
        this.timerDisplay.className = ''; // Remove warning/danger classes
    }

    updateTimer() {
        if (!this.sessionStartTime) return;

        const now = Date.now();
        const elapsed = now - this.sessionStartTime;

        let displayTime, timeClass = '';

        if (this.showElapsed) {
            displayTime = elapsed;
        } else {
            // Remaining time
            displayTime = Math.max(0, this.sessionDuration - elapsed);
        }

        // Determine color based on progress
        const progress = elapsed / this.sessionDuration;
        if (progress >= 0.75) { // 75% through (1 hour remaining in 4-hour session)
            timeClass = 'danger';
        } else if (progress >= 0.5) { // 50% through (2 hours elapsed)
            timeClass = 'warning';
        }

        this.timerDisplay.textContent = this.formatTime(displayTime);
        this.timerDisplay.className = timeClass;
    }

    formatTime(milliseconds) {
        const totalSeconds = Math.floor(milliseconds / 1000);
        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        const seconds = totalSeconds % 60;

        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    toggleTimerMode() {
        this.showElapsed = !this.showElapsed;
        this.timerToggle.textContent = this.showElapsed ? 'Elapsed' : 'Remaining';
        this.updateTimer(); // Update display immediately
    }

    // Debug Console Methods
    showDebugConsole() {
        this.debugConsoleModal.classList.remove('hidden');
        this.updateDebugMessageCount();
        this.syncConsoleMessages(); // Initial sync
        this.startConsoleSyncTimer(); // Start periodic sync
    }

    closeDebugConsole() {
        this.debugConsoleModal.classList.add('hidden');
        this.stopConsoleSyncTimer(); // Stop periodic sync
    }

    addDebugMessage(source, message, level = 'info') {
        const debugMessage = {
            id: ++this.debugMessageId,
            timestamp: new Date(),
            source: source,
            message: message,
            level: level
        };

        this.debugMessages.push(debugMessage);
        this.renderDebugMessage(debugMessage);
        this.updateDebugMessageCount();

        // Auto-scroll if enabled
        if (this.debugAutoScroll.checked) {
            this.debugConsoleContent.scrollTop = this.debugConsoleContent.scrollHeight;
        }

        // Limit message history to prevent memory issues
        if (this.debugMessages.length > 1000) {
            this.debugMessages = this.debugMessages.slice(-500);
            this.refreshDebugConsole();
        }
    }

    renderDebugMessage(debugMessage) {
        const messageElement = document.createElement('div');
        messageElement.className = `debug-message ${debugMessage.level}`;
        messageElement.dataset.messageId = debugMessage.id;
        messageElement.dataset.level = debugMessage.level;

        const timestamp = debugMessage.timestamp.toLocaleTimeString('en-US', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });

        messageElement.innerHTML = `
            <div class="debug-message-header">
                <span class="debug-message-source">${debugMessage.source}</span>
                <span class="debug-message-timestamp">${timestamp}</span>
            </div>
            <div class="debug-message-content">${this.escapeHtml(debugMessage.message)}</div>
        `;

        this.debugConsoleContent.appendChild(messageElement);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    clearDebugConsole() {
        this.debugMessages = [];
        this.debugConsoleContent.innerHTML = '';
        this.updateDebugMessageCount();
    }

    copyDebugConsole() {
        const messages = this.getFilteredDebugMessages();
        const text = messages.map(msg => {
            const timestamp = msg.timestamp.toISOString();
            return `[${timestamp}] ${msg.source.toUpperCase()}: ${msg.message}`;
        }).join('\n');

        navigator.clipboard.writeText(text).then(() => {
            this.updateStatus('Debug console copied to clipboard');
        }).catch(err => {
            console.error('Failed to copy to clipboard:', err);
            this.updateStatus('Failed to copy to clipboard');
        });
    }

    exportDebugConsole() {
        const messages = this.getFilteredDebugMessages();
        const text = messages.map(msg => {
            const timestamp = msg.timestamp.toISOString();
            return `[${timestamp}] ${msg.source.toUpperCase()}: ${msg.message}`;
        }).join('\n');

        const blob = new Blob([text], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `debug-console-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.updateStatus('Debug console exported');
    }

    filterDebugMessages() {
        const filterLevel = this.debugFilterLevel.value;
        const messages = this.debugConsoleContent.querySelectorAll('.debug-message');

        messages.forEach(messageElement => {
            const level = messageElement.dataset.level;
            if (filterLevel === 'all' || level === filterLevel) {
                messageElement.style.display = 'block';
            } else {
                messageElement.style.display = 'none';
            }
        });
    }

    getFilteredDebugMessages() {
        const filterLevel = this.debugFilterLevel.value;
        if (filterLevel === 'all') {
            return this.debugMessages;
        }
        return this.debugMessages.filter(msg => msg.level === filterLevel);
    }

    refreshDebugConsole() {
        this.debugConsoleContent.innerHTML = '';
        this.debugMessages.forEach(msg => this.renderDebugMessage(msg));
        this.filterDebugMessages();
    }

    updateDebugMessageCount() {
        const filteredCount = this.getFilteredDebugMessages().length;
        const totalCount = this.debugMessages.length;
        this.debugMessageCount.textContent = `${filteredCount}/${totalCount} messages`;
    }

    // Sync console messages from backend
    async syncConsoleMessages() {
        try {
            const backendMessages = await window.go.main.App.GetConsoleMessages();

            // Add any new messages from backend that we don't have
            backendMessages.forEach(backendMsg => {
                const exists = this.debugMessages.some(localMsg => localMsg.id === backendMsg.id);
                if (!exists) {
                    const debugMessage = {
                        id: backendMsg.id,
                        timestamp: new Date(backendMsg.timestamp),
                        source: backendMsg.source,
                        message: backendMsg.message,
                        level: backendMsg.level
                    };
                    this.debugMessages.push(debugMessage);
                    this.renderDebugMessage(debugMessage);
                }
            });

            this.updateDebugMessageCount();

            // Auto-scroll if enabled
            if (this.debugAutoScroll.checked) {
                this.debugConsoleContent.scrollTop = this.debugConsoleContent.scrollHeight;
            }
        } catch (error) {
            console.error('Failed to sync console messages:', error);
        }
    }

    // Start periodic sync of console messages
    startConsoleSyncTimer() {
        // Sync every 2 seconds when debug console is open
        this.consoleSyncInterval = setInterval(() => {
            if (!this.debugConsoleModal.classList.contains('hidden')) {
                this.syncConsoleMessages();
            }
        }, 2000);
    }

    // Stop console sync timer
    stopConsoleSyncTimer() {
        if (this.consoleSyncInterval) {
            clearInterval(this.consoleSyncInterval);
            this.consoleSyncInterval = null;
        }
    }

    // Cleanup method
    destroy() {
        this.stopPeriodicRefresh();
        this.stopTimer();
        this.stopConsoleSyncTimer();
    }
}

// Initialize the HUD when the page loads
document.addEventListener('DOMContentLoaded', () => {
    window.hud = new DMScreenHUD();
});

// Cleanup when the window is closed
window.addEventListener('beforeunload', () => {
    if (window.hud) {
        window.hud.destroy();
    }
});
