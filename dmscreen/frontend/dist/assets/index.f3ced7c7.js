(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))s(i);new MutationObserver(i=>{for(const n of i)if(n.type==="childList")for(const a of n.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&s(a)}).observe(document,{childList:!0,subtree:!0});function t(i){const n={};return i.integrity&&(n.integrity=i.integrity),i.referrerpolicy&&(n.referrerPolicy=i.referrerpolicy),i.crossorigin==="use-credentials"?n.credentials="include":i.crossorigin==="anonymous"?n.credentials="omit":n.credentials="same-origin",n}function s(i){if(i.ep)return;i.ep=!0;const n=t(i);fetch(i.href,n)}})();class r{constructor(){this.messages=[],this.logText="",this.messageList=document.getElementById("message-list"),this.statusElement=document.getElementById("session-status"),this.clearBtn=document.getElementById("clear-btn"),this.settingsBtn=document.getElementById("settings-btn"),this.textCanvas=document.getElementById("text-canvas"),this.clearLogBtn=document.getElementById("clear-log-btn"),this.commandInput=document.getElementById("command-input"),this.executeBtn=document.getElementById("execute-btn"),this.startSessionBtn=document.getElementById("start-session-btn"),this.stopSessionBtn=document.getElementById("stop-session-btn"),this.textCanvasUpdateInterval=null,this.preferencesModal=document.getElementById("preferences-modal"),this.preferencesForm=document.getElementById("preferences-form"),this.preferencesClose=document.getElementById("preferences-close"),this.preferencesSave=document.getElementById("preferences-save"),this.preferencesCancel=document.getElementById("preferences-cancel"),this.dndVersionRadios=document.querySelectorAll('input[name="dnd-version"]'),this.geminiApiKeyInput=document.getElementById("gemini-api-key"),this.assemblyAIApiKeyInput=document.getElementById("assemblyai-api-key"),this.debugConsoleBtn=document.getElementById("debug-console-btn"),this.debugConsoleModal=document.getElementById("debug-console-modal"),this.debugConsoleClose=document.getElementById("debug-console-close"),this.debugConsoleContent=document.getElementById("debug-console-content"),this.debugClearBtn=document.getElementById("debug-clear-btn"),this.debugCopyBtn=document.getElementById("debug-copy-btn"),this.debugExportBtn=document.getElementById("debug-export-btn"),this.debugMessageCount=document.getElementById("debug-message-count"),this.debugAutoScroll=document.getElementById("debug-auto-scroll"),this.debugFilterLevel=document.getElementById("debug-filter-level"),this.debugMessages=[],this.debugMessageId=0,this.sessionActive=!1,this.lastContentLength=0,this.timerDisplay=document.getElementById("timer-display"),this.timerToggle=document.getElementById("timer-toggle"),this.sessionStartTime=null,this.timerInterval=null,this.showElapsed=!0,this.sessionDuration=4*60*60*1e3,this.initializeEventListeners(),this.updateStatus("Ready"),this.addSampleMessages(),this.addSampleLogText()}initializeEventListeners(){this.clearBtn.addEventListener("click",()=>this.clearMessages()),this.debugConsoleBtn.addEventListener("click",()=>this.showDebugConsole()),this.settingsBtn.addEventListener("click",()=>this.showSettings()),this.clearLogBtn.addEventListener("click",()=>this.clearLog()),this.executeBtn.addEventListener("click",()=>this.executeCommand()),this.startSessionBtn.addEventListener("click",()=>this.startSession()),this.stopSessionBtn.addEventListener("click",()=>this.stopSession()),this.timerToggle.addEventListener("click",()=>this.toggleTimerMode()),this.debugConsoleClose.addEventListener("click",()=>this.closeDebugConsole()),this.debugClearBtn.addEventListener("click",()=>this.clearDebugConsole()),this.debugCopyBtn.addEventListener("click",()=>this.copyDebugConsole()),this.debugExportBtn.addEventListener("click",()=>this.exportDebugConsole()),this.debugFilterLevel.addEventListener("change",()=>this.filterDebugMessages()),this.debugConsoleModal.addEventListener("click",e=>{e.target===this.debugConsoleModal&&this.closeDebugConsole()}),this.preferencesClose.addEventListener("click",e=>{e.preventDefault(),this.closePreferences()}),this.preferencesSave.addEventListener("click",e=>{e.preventDefault(),this.savePreferences()}),this.preferencesCancel.addEventListener("click",e=>{e.preventDefault(),this.closePreferences()}),this.preferencesModal.addEventListener("click",e=>{e.target===this.preferencesModal&&this.closePreferences()}),this.geminiApiKeyInput.addEventListener("blur",()=>this.maskApiKeyInput(this.geminiApiKeyInput)),this.assemblyAIApiKeyInput.addEventListener("blur",()=>this.maskApiKeyInput(this.assemblyAIApiKeyInput)),this.preferencesForm.addEventListener("submit",e=>(e.preventDefault(),!1)),this.commandInput.addEventListener("keydown",e=>{e.key==="Enter"&&this.executeCommand()})}addMessage(e,t,s=null){const i={id:Date.now(),type:e,content:t,imagePath:s,timestamp:new Date};this.messages.unshift(i),this.renderMessage(i),this.updateStatus(`${this.messages.length} messages`)}renderMessage(e){const t=document.createElement("div");t.className="message-item",t.dataset.messageId=e.id,t.innerHTML=`
            <div class="message-header">
                <span class="message-type">${e.type}</span>
                <span class="message-time">${this.formatTime(e.timestamp)}</span>
            </div>
            <div class="message-content">${e.content}</div>
            <div class="message-actions">
                <button class="action-btn gallery" onclick="hud.sendToGallery(${e.id})">\u{1F4F8} Gallery</button>
                <button class="action-btn log" onclick="hud.logMessage(${e.id})">\u{1F4DD} Log</button>
            </div>
        `,this.messageList.insertBefore(t,this.messageList.firstChild)}formatTime(e){return e.toLocaleTimeString("en-US",{hour12:!1,hour:"2-digit",minute:"2-digit"})}clearMessages(){this.messages=[],this.messageList.innerHTML="",this.updateStatus("Messages cleared")}sendToGallery(e){const t=this.messages.find(s=>s.id===e);t&&(console.log("Sending to gallery:",t),this.updateStatus(`Sent "${t.type}" to gallery`))}logMessage(e){const t=this.messages.find(s=>s.id===e);t&&(console.log("Logging message:",t),this.updateStatus(`Logged "${t.type}" message`))}async showSettings(){try{const e=await window.go.main.App.GetSettings();this.populatePreferencesForm(e),this.preferencesModal.classList.remove("hidden")}catch(e){console.error("Failed to load settings:",e),this.updateStatus("Failed to load settings")}}closePreferences(){this.preferencesModal.classList.add("hidden")}async savePreferences(){try{const e=this.getPreferencesFromForm(),t=await window.go.main.App.SaveSettings(e);t.includes("successfully")?(this.updateStatus("Settings saved"),this.closePreferences()):this.updateStatus(t)}catch(e){console.error("Failed to save settings:",e),this.updateStatus("Failed to save settings")}}populatePreferencesForm(e){this.dndVersionRadios.forEach(t=>{t.checked=t.value===e.dndVersion}),this.geminiApiKeyInput.value=e.geminiAPIKey||"",this.geminiApiKeyInput.dataset.isMasked=e.geminiAPIKey&&e.geminiAPIKey.includes("***")?"true":"false",this.geminiApiKeyInput.dataset.hasValue=e.geminiAPIKey?"true":"false",this.assemblyAIApiKeyInput.value=e.assemblyAIKey||"",this.assemblyAIApiKeyInput.dataset.isMasked=e.assemblyAIKey&&e.assemblyAIKey.includes("***")?"true":"false",this.assemblyAIApiKeyInput.dataset.hasValue=e.assemblyAIKey?"true":"false"}getPreferencesFromForm(){const e=document.querySelector('input[name="dnd-version"]:checked'),t=e?e.value:"2024";let s=this.geminiApiKeyInput.value.trim(),i=this.assemblyAIApiKeyInput.value.trim();return{dndVersion:t,geminiAPIKey:s,assemblyAIKey:i}}maskApiKeyInput(e){const t=e.value.trim();if(t&&!t.includes("*")&&t.length>7){const s=t.length-7,i=t.substring(0,3)+"*".repeat(s)+t.substring(t.length-4);e.value=i,e.dataset.originalValue=t}else if(t&&!t.includes("*")&&t.length<=7){const s="*".repeat(t.length);e.value=s,e.dataset.originalValue=t}}appendToLog(e){this.logText+=e+`
`,this.textCanvas.textContent=this.logText,this.textCanvas.scrollTop=this.textCanvas.scrollHeight}async clearLog(){try{await window.go.main.App.ClearTextCanvas(),this.logText="",this.textCanvas.textContent="",this.updateStatus("Log cleared")}catch(e){console.error("Failed to clear log:",e),this.updateStatus("Clear failed")}}startPeriodicRefresh(){if(!this.sessionActive)return;const e=1e4;this.textCanvasUpdateInterval=setInterval(async()=>{if(!this.sessionActive){this.stopPeriodicRefresh();return}await this.refreshTextCanvas()},e)}stopPeriodicRefresh(){this.textCanvasUpdateInterval&&(clearInterval(this.textCanvasUpdateInterval),this.textCanvasUpdateInterval=null)}executeCommand(){const e=this.commandInput.value.trim();!e||(this.updateStatus(`Executing: ${e}`),this.appendToLog(`> ${e}`),this.processCommand(e),this.commandInput.value="")}async processCommand(e){const t=e.toLowerCase();if(t.startsWith("search ")){const s=e.substring(7);await this.sendToLocalscribeMetadata(`search command: "${s}"`)}else if(t==="clear")this.clearMessages(),await this.sendToLocalscribeMetadata("messages cleared via command");else if(t==="stats")try{const s=await window.go.main.App.GetTextCanvasStats();await this.sendToLocalscribeMetadata(`buffer stats: ${s.lines} lines, ${s.sizeKB.toFixed(1)} KB`)}catch{await this.sendToLocalscribeMetadata("failed to get buffer stats")}else t==="help"?await this.sendToLocalscribeMetadata("help requested - available commands: search, clear, stats, help"):await this.sendToLocalscribeMetadata(`unknown command: "${e}"`)}async sendToLocalscribeMetadata(e){try{const t=await fetch("http://localhost:8080/metadata?body="+encodeURIComponent(e));t.ok||console.error("Failed to send metadata to localscribe:",t.statusText)}catch(t){console.error("Failed to send metadata to localscribe:",t),this.updateStatus(`Command: ${e}`)}}async startSession(){this.updateStatus("Starting session..."),this.startSessionBtn.disabled=!0;try{const e=await window.go.main.App.StartLocalscribeLibrary();console.log(`Session started: ${e}`),this.addDebugMessage("dmscreen",`Session started: ${e}`,"info"),this.updateStatus("Session active"),this.startSessionBtn.disabled=!0,this.stopSessionBtn.disabled=!1,this.sessionActive=!0,this.startTimer(),this.startPeriodicRefresh(),setTimeout(()=>this.refreshTextCanvas(),2e3)}catch(e){console.error(`Failed to start session: ${e}`),this.addDebugMessage("dmscreen",`Failed to start session: ${e}`,"error"),this.updateStatus("Start failed"),this.startSessionBtn.disabled=!1}}async refreshTextCanvas(){try{const e=await window.go.main.App.GetTextCanvasLineCount();if(e>this.lastContentLength){const t=await window.go.main.App.GetTextCanvasContentSince(this.lastContentLength);t&&t.length>0&&(this.logText?this.logText+=`
`+t.join(`
`):this.logText=t.join(`
`),this.textCanvas.textContent=this.logText,this.lastContentLength=e,this.textCanvas.scrollTop=this.textCanvas.scrollHeight)}}catch(e){console.debug("Manual refresh error:",e)}}async stopSession(){this.updateStatus("Stopping session..."),this.stopSessionBtn.disabled=!0;try{const e=await window.go.main.App.StopLocalscribeLibrary();console.log(`Session stopped: ${e}`),this.addDebugMessage("dmscreen",`Session stopped: ${e}`,"info"),this.updateStatus("Ready"),this.startSessionBtn.disabled=!1,this.stopSessionBtn.disabled=!0,this.sessionActive=!1,this.stopTimer(),this.stopPeriodicRefresh()}catch(e){console.error(`Failed to stop session: ${e}`),this.addDebugMessage("dmscreen",`Failed to stop session: ${e}`,"error"),this.updateStatus("Stop failed"),this.stopSessionBtn.disabled=!1}}updateStatus(e){this.statusElement.textContent=e,setTimeout(()=>{this.statusElement.textContent="Ready"},3e3)}addSampleMessages(){setTimeout(()=>{this.addMessage("NPC",'Bartender Gareth: "Welcome to the Prancing Pony, travelers!"')},1e3),setTimeout(()=>{this.addMessage("LORE","The ancient ruins of Shadowmere are said to contain powerful artifacts.")},2e3),setTimeout(()=>{this.addMessage("DC","Perception DC 15 to notice the hidden door behind the tapestry.")},3e3)}addSampleLogText(){setTimeout(()=>{this.addDebugMessage("dmscreen","Application initialized successfully","info"),this.addDebugMessage("localscribe","Transcription service ready","info"),this.addDebugMessage("system","Debug console activated","debug")},500)}startTimer(){this.resetTimer(),this.sessionStartTime=Date.now(),this.timerInterval=setInterval(()=>this.updateTimer(),1e3),this.updateTimer()}stopTimer(){this.timerInterval&&(clearInterval(this.timerInterval),this.timerInterval=null)}resetTimer(){this.sessionStartTime=null,this.timerDisplay.textContent="00:00:00",this.timerDisplay.className=""}updateTimer(){if(!this.sessionStartTime)return;const t=Date.now()-this.sessionStartTime;let s,i="";this.showElapsed?s=t:s=Math.max(0,this.sessionDuration-t);const n=t/this.sessionDuration;n>=.75?i="danger":n>=.5&&(i="warning"),this.timerDisplay.textContent=this.formatTime(s),this.timerDisplay.className=i}formatTime(e){const t=Math.floor(e/1e3),s=Math.floor(t/3600),i=Math.floor(t%3600/60),n=t%60;return`${s.toString().padStart(2,"0")}:${i.toString().padStart(2,"0")}:${n.toString().padStart(2,"0")}`}toggleTimerMode(){this.showElapsed=!this.showElapsed,this.timerToggle.textContent=this.showElapsed?"Elapsed":"Remaining",this.updateTimer()}showDebugConsole(){this.debugConsoleModal.classList.remove("hidden"),this.updateDebugMessageCount(),this.syncConsoleMessages(),this.startConsoleSyncTimer()}closeDebugConsole(){this.debugConsoleModal.classList.add("hidden"),this.stopConsoleSyncTimer()}addDebugMessage(e,t,s="info"){const i={id:++this.debugMessageId,timestamp:new Date,source:e,message:t,level:s};this.debugMessages.push(i),this.renderDebugMessage(i),this.updateDebugMessageCount(),this.debugAutoScroll.checked&&(this.debugConsoleContent.scrollTop=this.debugConsoleContent.scrollHeight),this.debugMessages.length>1e3&&(this.debugMessages=this.debugMessages.slice(-500),this.refreshDebugConsole())}renderDebugMessage(e){const t=document.createElement("div");t.className=`debug-message ${e.level}`,t.dataset.messageId=e.id,t.dataset.level=e.level;const s=e.timestamp.toLocaleTimeString("en-US",{hour12:!1,hour:"2-digit",minute:"2-digit",second:"2-digit"});t.innerHTML=`
            <div class="debug-message-header">
                <span class="debug-message-source">${e.source}</span>
                <span class="debug-message-timestamp">${s}</span>
            </div>
            <div class="debug-message-content">${this.escapeHtml(e.message)}</div>
        `,this.debugConsoleContent.appendChild(t)}escapeHtml(e){const t=document.createElement("div");return t.textContent=e,t.innerHTML}clearDebugConsole(){this.debugMessages=[],this.debugConsoleContent.innerHTML="",this.updateDebugMessageCount()}copyDebugConsole(){const t=this.getFilteredDebugMessages().map(s=>`[${s.timestamp.toISOString()}] ${s.source.toUpperCase()}: ${s.message}`).join(`
`);navigator.clipboard.writeText(t).then(()=>{this.updateStatus("Debug console copied to clipboard")}).catch(s=>{console.error("Failed to copy to clipboard:",s),this.updateStatus("Failed to copy to clipboard")})}exportDebugConsole(){const t=this.getFilteredDebugMessages().map(a=>`[${a.timestamp.toISOString()}] ${a.source.toUpperCase()}: ${a.message}`).join(`
`),s=new Blob([t],{type:"text/plain"}),i=URL.createObjectURL(s),n=document.createElement("a");n.href=i,n.download=`debug-console-${new Date().toISOString().slice(0,19).replace(/:/g,"-")}.txt`,document.body.appendChild(n),n.click(),document.body.removeChild(n),URL.revokeObjectURL(i),this.updateStatus("Debug console exported")}filterDebugMessages(){const e=this.debugFilterLevel.value;this.debugConsoleContent.querySelectorAll(".debug-message").forEach(s=>{const i=s.dataset.level;e==="all"||i===e?s.style.display="block":s.style.display="none"})}getFilteredDebugMessages(){const e=this.debugFilterLevel.value;return e==="all"?this.debugMessages:this.debugMessages.filter(t=>t.level===e)}refreshDebugConsole(){this.debugConsoleContent.innerHTML="",this.debugMessages.forEach(e=>this.renderDebugMessage(e)),this.filterDebugMessages()}updateDebugMessageCount(){const e=this.getFilteredDebugMessages().length,t=this.debugMessages.length;this.debugMessageCount.textContent=`${e}/${t} messages`}async syncConsoleMessages(){try{(await window.go.main.App.GetConsoleMessages()).forEach(t=>{if(!this.debugMessages.some(i=>i.id===t.id)){const i={id:t.id,timestamp:new Date(t.timestamp),source:t.source,message:t.message,level:t.level};this.debugMessages.push(i),this.renderDebugMessage(i)}}),this.updateDebugMessageCount(),this.debugAutoScroll.checked&&(this.debugConsoleContent.scrollTop=this.debugConsoleContent.scrollHeight)}catch(e){console.error("Failed to sync console messages:",e)}}startConsoleSyncTimer(){this.consoleSyncInterval=setInterval(()=>{this.debugConsoleModal.classList.contains("hidden")||this.syncConsoleMessages()},2e3)}stopConsoleSyncTimer(){this.consoleSyncInterval&&(clearInterval(this.consoleSyncInterval),this.consoleSyncInterval=null)}destroy(){this.stopPeriodicRefresh(),this.stopTimer(),this.stopConsoleSyncTimer()}}document.addEventListener("DOMContentLoaded",()=>{window.hud=new r});window.addEventListener("beforeunload",()=>{window.hud&&window.hud.destroy()});
