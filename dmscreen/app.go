package main

import (
	"context"
	"dmscreen/lib/localscribe"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"
)

// HUDMessage represents a message displayed in the HUD
type HUDMessage struct {
	ID        int64     `json:"id"`
	Type      string    `json:"type"`
	Content   string    `json:"content"`
	ImagePath string    `json:"imagePath,omitempty"`
	Timestamp time.Time `json:"timestamp"`
}

// AppSettings represents the application settings/preferences
type AppSettings struct {
	DNDVersion    string `json:"dndVersion"`    // "2014" or "2024"
	GeminiAPIKey  string `json:"geminiAPIKey"`  // Stored securely, masked in UI
	AssemblyAIKey string `json:"assemblyAIKey"` // Stored securely, masked in UI
}

// ConsoleMessage represents a debug console message
type ConsoleMessage struct {
	ID        int64     `json:"id"`
	Timestamp time.Time `json:"timestamp"`
	Source    string    `json:"source"`
	Message   string    `json:"message"`
	Level     string    `json:"level"`
}

// App struct
type App struct {
	ctx                context.Context
	messages           []HUDMessage
	isVisible          bool
	localscribeRunning bool
	localscribeMutex   sync.Mutex
	textCanvasBuffer   []string
	textCanvasMutex    sync.Mutex
	settings           AppSettings
	settingsMutex      sync.Mutex

	// Console system for debug messages
	consoleMessages   []ConsoleMessage
	consoleMutex      sync.Mutex
	consoleMessageID  int64
	consoleOutputChan chan localscribe.OutputMessage

	// Localscribe library integration
	transcriptionSession *localscribe.TranscriptionSession
}

// NewApp creates a new App application struct
func NewApp() *App {
	return &App{
		messages:          make([]HUDMessage, 0),
		isVisible:         true,
		textCanvasBuffer:  make([]string, 0),
		consoleMessages:   make([]ConsoleMessage, 0),
		consoleOutputChan: make(chan localscribe.OutputMessage, 100),
		settings: AppSettings{
			DNDVersion:    "2024", // Default to 2024 edition
			GeminiAPIKey:  os.Getenv("GEMINI_API_KEY"),
			AssemblyAIKey: os.Getenv("ASSEMBLYAI_API_KEY"),
		},
	}
}

// startup is called when the app starts. The context is saved
// so we can call the runtime methods
func (a *App) startup(ctx context.Context) {
	a.ctx = ctx
	fmt.Println("DM Screen started")

	// Initialize console with startup messages
	a.AddConsoleMessage("dmscreen", "DM Screen application started", "info")
	a.AddConsoleMessage("system", "Debug console initialized", "debug")

	// Start the global hotkey listener for Cmd+D
	go func() {
		if err := StartHotkeyListener(ctx, func() {
			// Toggle window visibility when Cmd+D is pressed
			ToggleWindowVisibility(a.ctx, &a.isVisible)
		}); err != nil {
			a.AddConsoleMessage("hotkeys", fmt.Sprintf("Hotkey listener error: %v", err), "error")
			fmt.Printf("Hotkey listener error: %v\n", err)
		} else {
			a.AddConsoleMessage("hotkeys", "Global hotkey listener started (Cmd+D)", "info")
		}
	}()

	// Start console output processor for localscribe integration
	go a.processConsoleOutput()
}

// AddMessage adds a new message to the HUD
func (a *App) AddMessage(msgType, content string) HUDMessage {
	message := HUDMessage{
		ID:        time.Now().UnixNano(),
		Type:      msgType,
		Content:   content,
		Timestamp: time.Now(),
	}

	a.messages = append([]HUDMessage{message}, a.messages...) // Prepend to slice

	// Keep only the last 50 messages to prevent memory issues
	if len(a.messages) > 50 {
		a.messages = a.messages[:50]
	}

	return message
}

// GetMessages returns all current messages
func (a *App) GetMessages() []HUDMessage {
	return a.messages
}

// ClearMessages removes all messages
func (a *App) ClearMessages() {
	a.messages = make([]HUDMessage, 0)
}

// SendToGallery handles sending content to the gallery
func (a *App) SendToGallery(messageID int64) string {
	for _, msg := range a.messages {
		if msg.ID == messageID {
			// TODO: Implement actual gallery integration
			fmt.Printf("Sending to gallery: %s - %s\n", msg.Type, msg.Content)
			return fmt.Sprintf("Sent '%s' to gallery", msg.Type)
		}
	}
	return "Message not found"
}

// LogMessage handles logging a message
func (a *App) LogMessage(messageID int64) string {
	for _, msg := range a.messages {
		if msg.ID == messageID {
			// TODO: Implement actual logging functionality
			fmt.Printf("Logging message: %s - %s\n", msg.Type, msg.Content)
			return fmt.Sprintf("Logged '%s' message", msg.Type)
		}
	}
	return "Message not found"
}

// ToggleVisibility toggles the window visibility (can be called from frontend)
func (a *App) ToggleVisibility() {
	ToggleWindowVisibility(a.ctx, &a.isVisible)
}

// EnableHotkeyDebug enables debug logging for hotkeys
func (a *App) EnableHotkeyDebug() {
	DebugHotkeys = true
	fmt.Println("Hotkey debug mode enabled")
}

// GetVisibilityState returns the current visibility state
func (a *App) GetVisibilityState() bool {
	return a.isVisible
}

// StartLocalscribe starts the localscribe library (integrated approach)
func (a *App) StartLocalscribe() string {
	// Delegate to the new library-based implementation
	return a.StartLocalscribeLibrary()
}

// StopLocalscribe stops the localscribe library (integrated approach)
func (a *App) StopLocalscribe() string {
	// Delegate to the new library-based implementation
	return a.StopLocalscribeLibrary()
}

// IsLocalscribeRunning returns whether localscribe is currently running
func (a *App) IsLocalscribeRunning() bool {
	a.localscribeMutex.Lock()
	defer a.localscribeMutex.Unlock()
	return a.localscribeRunning
}

// StartLocalscribeLibrary starts localscribe using the integrated library
func (a *App) StartLocalscribeLibrary() string {
	a.localscribeMutex.Lock()
	defer a.localscribeMutex.Unlock()

	if a.localscribeRunning {
		return "Localscribe is already running"
	}

	// Check if AssemblyAI API key is available
	a.settingsMutex.Lock()
	assemblyAIKey := a.settings.AssemblyAIKey
	a.settingsMutex.Unlock()

	if assemblyAIKey == "" {
		a.AddConsoleMessage("localscribe", "AssemblyAI API key is required", "error")
		return "AssemblyAI API key is required. Please set it in Preferences first."
	}

	// Create a specific log file for this session
	logFilePath, err := a.createSessionLogFile()
	if err != nil {
		a.AddConsoleMessage("localscribe", fmt.Sprintf("Failed to create log file: %v", err), "error")
		return fmt.Sprintf("Failed to create log file: %v", err)
	}

	a.AddConsoleMessage("localscribe", fmt.Sprintf("Creating transcription session with log file: %s", logFilePath), "info")

	// Create library configuration
	config := localscribe.NewLibraryConfig(assemblyAIKey, logFilePath, a.consoleOutputChan)
	config.LogToConsole = false // We handle console output through our debug system

	// Create transcription session
	session, err := localscribe.NewTranscriptionSession(config)
	if err != nil {
		a.AddConsoleMessage("localscribe", fmt.Sprintf("Failed to create transcription session: %v", err), "error")
		return fmt.Sprintf("Failed to create transcription session: %v", err)
	}

	// Start the session
	if err := session.Start(); err != nil {
		a.AddConsoleMessage("localscribe", fmt.Sprintf("Failed to start transcription session: %v", err), "error")
		return fmt.Sprintf("Failed to start transcription session: %v", err)
	}

	a.transcriptionSession = session
	a.localscribeRunning = true

	a.AddConsoleMessage("localscribe", "Transcription session started successfully", "info")

	// Start processing transcription output
	go a.processTranscriptionOutput()

	return "Localscribe library started successfully"
}

// StopLocalscribeLibrary stops the localscribe library session
func (a *App) StopLocalscribeLibrary() string {
	a.localscribeMutex.Lock()
	defer a.localscribeMutex.Unlock()

	if !a.localscribeRunning || a.transcriptionSession == nil {
		return "Localscribe library is not running"
	}

	a.AddConsoleMessage("localscribe", "Stopping transcription session", "info")

	if err := a.transcriptionSession.Stop(); err != nil {
		a.AddConsoleMessage("localscribe", fmt.Sprintf("Error stopping transcription session: %v", err), "warning")
	}

	a.transcriptionSession = nil
	a.localscribeRunning = false

	a.AddConsoleMessage("localscribe", "Transcription session stopped", "info")
	return "Localscribe library stopped"
}

// processTranscriptionOutput processes transcription output from the library
func (a *App) processTranscriptionOutput() {
	if a.transcriptionSession == nil {
		return
	}

	outputChan := a.transcriptionSession.GetOutputChannel()
	for transcript := range outputChan {
		// Add transcript to text canvas
		a.sendToTextCanvas(transcript)
	}
}

// Legacy binary methods removed - now using integrated library approach

// Legacy process output reading removed - now using integrated library approach

// sendToTextCanvas sends a line of text to the frontend text canvas (internal method)
func (a *App) sendToTextCanvas(text string) {
	a.textCanvasMutex.Lock()

	// Add timestamp
	timestampedText := fmt.Sprintf("[%s] %s", time.Now().Format("15:04:05"), text)

	// Add to buffer
	a.textCanvasBuffer = append(a.textCanvasBuffer, timestampedText)

	// Keep only the last 10,000 lines to handle 4+ hour sessions
	// At ~25 lines per KB, this supports ~400KB of text (6+ hour sessions)
	if len(a.textCanvasBuffer) > 10000 {
		a.textCanvasBuffer = a.textCanvasBuffer[len(a.textCanvasBuffer)-10000:]
	}

	a.textCanvasMutex.Unlock()

	// Emit event to frontend with the new line (no polling needed!)
	if a.ctx != nil {
		// Import will be added when we use runtime.EventsEmit
		// runtime.EventsEmit(a.ctx, "textCanvasUpdate", timestampedText)
		// For now, we'll keep the polling approach but make it much less frequent
	}
}

// GetTextCanvasContent returns all text canvas content
func (a *App) GetTextCanvasContent() []string {
	a.textCanvasMutex.Lock()
	defer a.textCanvasMutex.Unlock()

	// Return a copy of the buffer
	result := make([]string, len(a.textCanvasBuffer))
	copy(result, a.textCanvasBuffer)
	return result
}

// GetTextCanvasContentSince returns content since a given line count
func (a *App) GetTextCanvasContentSince(lastLineCount int) []string {
	a.textCanvasMutex.Lock()
	defer a.textCanvasMutex.Unlock()

	// If we have new content since the last check
	if len(a.textCanvasBuffer) > lastLineCount {
		// Return only the new lines
		newLines := a.textCanvasBuffer[lastLineCount:]
		result := make([]string, len(newLines))
		copy(result, newLines)
		return result
	}

	// No new content
	return []string{}
}

// GetTextCanvasLineCount returns just the current line count (lightweight check)
func (a *App) GetTextCanvasLineCount() int {
	a.textCanvasMutex.Lock()
	defer a.textCanvasMutex.Unlock()
	return len(a.textCanvasBuffer)
}

// Legacy log file tailing removed - now using integrated library approach

// Legacy log file tailing methods removed - now using integrated library approach

// Legacy log line buffer methods removed - now using integrated library approach

// ClearTextCanvas clears the text canvas buffer
func (a *App) ClearTextCanvas() {
	a.textCanvasMutex.Lock()
	defer a.textCanvasMutex.Unlock()
	a.textCanvasBuffer = make([]string, 0)
}

// GetTextCanvasStats returns statistics about the text canvas buffer
func (a *App) GetTextCanvasStats() map[string]interface{} {
	a.textCanvasMutex.Lock()
	defer a.textCanvasMutex.Unlock()

	totalChars := 0
	for _, line := range a.textCanvasBuffer {
		totalChars += len(line)
	}

	return map[string]interface{}{
		"lines":     len(a.textCanvasBuffer),
		"totalSize": totalChars,
		"sizeKB":    float64(totalChars) / 1024.0,
	}
}

// Console Methods

// AddConsoleMessage adds a message to the debug console
func (a *App) AddConsoleMessage(source, message, level string) {
	a.consoleMutex.Lock()
	defer a.consoleMutex.Unlock()

	a.consoleMessageID++
	consoleMsg := ConsoleMessage{
		ID:        a.consoleMessageID,
		Timestamp: time.Now(),
		Source:    source,
		Message:   message,
		Level:     level,
	}

	a.consoleMessages = append(a.consoleMessages, consoleMsg)

	// Keep only the last 1000 messages to prevent memory issues
	if len(a.consoleMessages) > 1000 {
		a.consoleMessages = a.consoleMessages[len(a.consoleMessages)-1000:]
	}

	// Also log to stdout for development
	fmt.Printf("[CONSOLE] [%s] %s: %s\n", level, source, message)
}

// GetConsoleMessages returns all console messages
func (a *App) GetConsoleMessages() []ConsoleMessage {
	a.consoleMutex.Lock()
	defer a.consoleMutex.Unlock()

	// Return a copy
	result := make([]ConsoleMessage, len(a.consoleMessages))
	copy(result, a.consoleMessages)
	return result
}

// ClearConsoleMessages clears all console messages
func (a *App) ClearConsoleMessages() {
	a.consoleMutex.Lock()
	defer a.consoleMutex.Unlock()
	a.consoleMessages = make([]ConsoleMessage, 0)
}

// GetConsoleMessageCount returns the number of console messages
func (a *App) GetConsoleMessageCount() int {
	a.consoleMutex.Lock()
	defer a.consoleMutex.Unlock()
	return len(a.consoleMessages)
}

// processConsoleOutput processes messages from the localscribe output channel
func (a *App) processConsoleOutput() {
	for msg := range a.consoleOutputChan {
		a.AddConsoleMessage(msg.Source, msg.Message, msg.Level)
	}
}

// createSessionLogFile creates a timestamped log file for the session
func (a *App) createSessionLogFile() (string, error) {
	// Get home directory
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return "", fmt.Errorf("failed to get home directory: %w", err)
	}

	// Create log directory if it doesn't exist
	logDir := filepath.Join(homeDir, ".local", "scribe")
	if err := os.MkdirAll(logDir, 0755); err != nil {
		return "", fmt.Errorf("failed to create log directory: %w", err)
	}

	// Generate timestamped filename
	timestamp := time.Now().Format("20060102_1504")
	logFileName := fmt.Sprintf("transcription-%s.log", timestamp)
	logFilePath := filepath.Join(logDir, logFileName)

	// Touch the file to create it
	file, err := os.Create(logFilePath)
	if err != nil {
		return "", fmt.Errorf("failed to create log file: %w", err)
	}
	file.Close()

	fmt.Printf("Created session log file: %s\n", logFilePath)
	return logFilePath, nil
}

// Legacy embedded binary testing removed - now using integrated library approach

// GetSettings returns the current application settings
func (a *App) GetSettings() AppSettings {
	a.settingsMutex.Lock()
	defer a.settingsMutex.Unlock()

	// Return a copy with masked API keys for UI display
	settings := a.settings
	settings.GeminiAPIKey = a.maskAPIKey(settings.GeminiAPIKey)
	settings.AssemblyAIKey = a.maskAPIKey(settings.AssemblyAIKey)
	return settings
}

// SaveSettings saves the application settings
func (a *App) SaveSettings(settings AppSettings) string {
	a.settingsMutex.Lock()
	defer a.settingsMutex.Unlock()

	// Validate D&D version
	if settings.DNDVersion != "2014" && settings.DNDVersion != "2024" {
		return "Invalid D&D version. Must be '2014' or '2024'"
	}

	// Handle Gemini API key
	if settings.GeminiAPIKey == "" {
		// Empty string means use environment variable
		settings.GeminiAPIKey = os.Getenv("GEMINI_API_KEY")
	} else if a.isAPIKeyMasked(settings.GeminiAPIKey) {
		// If API key is masked (contains asterisks), keep the existing key
		settings.GeminiAPIKey = a.settings.GeminiAPIKey
	}
	// Otherwise, use the provided key as-is

	// Handle AssemblyAI API key
	if settings.AssemblyAIKey == "" {
		// Empty string means use environment variable
		settings.AssemblyAIKey = os.Getenv("ASSEMBLYAI_API_KEY")
	} else if a.isAPIKeyMasked(settings.AssemblyAIKey) {
		// If API key is masked (contains asterisks), keep the existing key
		settings.AssemblyAIKey = a.settings.AssemblyAIKey
	}
	// Otherwise, use the provided key as-is

	// Update settings
	a.settings = settings

	// TODO: Persist settings to file or preferences

	return "Settings saved successfully"
}

// isAPIKeyMasked checks if an API key is masked (contains asterisks)
func (a *App) isAPIKeyMasked(apiKey string) bool {
	return strings.Contains(apiKey, "*")
}

// maskAPIKey masks an API key for display, showing first 3 and last 4 characters
// with the middle filled with asterisks to preserve the original length
func (a *App) maskAPIKey(apiKey string) string {
	if apiKey == "" {
		return ""
	}

	if len(apiKey) <= 7 {
		// If key is too short, just show asterisks matching the length
		return strings.Repeat("*", len(apiKey))
	}

	// Show first 3 and last 4 characters with asterisks filling the middle
	middleLength := len(apiKey) - 7 // Total length minus first 3 and last 4
	return apiKey[:3] + strings.Repeat("*", middleLength) + apiKey[len(apiKey)-4:]
}
