package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"os"
	"os/signal"
	"path"
	"syscall"
	"time"

	"dmscreen/lib/localscribe"

	"github.com/gordonklaus/portaudio"
)

var (
	defaultNowString = time.Now().Format("20060102_1504")
	defaultLogFile   = path.Join(os.Getenv("HOME"), ".local", "scribe", "transcription-"+defaultNowString+".log")
)

func initConfig(ctx context.Context) localscribe.Config {
	logFileEnv := os.Getenv("TRANSCRIPTION_FILE")
	assemblyAIKey := os.Getenv("ASSEMBLYAI_API_KEY")

	cfg := localscribe.Config{
		LogFile:       defaultLogFile,
		AssemblyAIKey: assemblyAIKey,
		Context:       ctx,
	}

	flag.IntVar(&cfg.RESTPort, "restPort", 8080, "Port to listen on for REST queries")
	flag.IntVar(&cfg.<PERSON>ple<PERSON>ate, "sampleRate", 16000, "Audio sample rate (Hz)")
	flag.IntVar(&cfg.FramesPerBuffer, "framesPerBuffer", 3200,
		"Number of frames to process per buffer")
	flag.StringVar(&cfg.LogFile, "l", strWithFallback(logFileEnv, cfg.LogFile),
		"Path to the transcription log file (default: TRANSCRIPTION_FILE or time-based).")
	flag.StringVar(&cfg.AssemblyAIKey, "k", cfg.AssemblyAIKey,
		"AssemblyAI API key (default: ASSEMBLYAI_API_KEY).")
	flag.BoolVar(&cfg.TranscriptOnly, "t", false, "Transcript only mode - disable optional logging (ipinfo, chrome, zoom)")
	flag.BoolVar(&cfg.TranscriptOnly, "transcriptOnly", false, "Transcript only mode - disable optional logging (ipinfo, chrome, zoom)")

	flag.Parse()

	if cfg.AssemblyAIKey == "" {
		fmt.Fprintln(os.Stderr, "Error: Must provide -k <APIKEY> or set ASSEMBLYAI_API_KEY env var")
		os.Exit(1)
	}

	return cfg
}

// strWithFallback returns str if not empty, otherwise fallback.
func strWithFallback(str, fallback string) string {
	if str != "" {
		return str
	}
	return fallback
}

func initPortAudio() {
	log.Println("initializing PortAudio")
	if err := portaudio.Initialize(); err != nil {
		log.Fatalf("portaudio init failed: %v\n", err)
	}
}

func main() {
	ctx, cancel := context.WithCancel(context.Background())
	cfg := initConfig(ctx)

	initPortAudio()
	defer portaudio.Terminate()

	log.Printf("starting voice recorder: sampleRate=%d, framesPerBuffer=%d\n",
		cfg.SampleRate, cfg.FramesPerBuffer)
	rec, err := localscribe.NewRecorder(cfg.SampleRate, cfg.FramesPerBuffer)
	if err != nil {
		log.Fatalf("failed to create recorder: %v\n", err)
	}

	backend := localscribe.NewAssemblyAIBackend(cfg)
	if backend == nil {
		log.Fatalf("failed to create AssemblyAI backend")
	}

	log.Printf("transcribing to file: %s\n", cfg.LogFile)

	go func() {
		sigCh := make(chan os.Signal, 1)
		// Listen for Ctrl-C (SIGINT) and Ctrl-Z (SIGTSTP), plus SIGTERM
		signal.Notify(sigCh, os.Interrupt, syscall.SIGTERM, syscall.SIGTSTP)
		for {
			s := <-sigCh
			switch s {
			case os.Interrupt, syscall.SIGTERM:
				// Pause sending so we don't get session-closed errors.
				localscribe.SetPaused(true)
				log.Println("\r\nlocalscribe: shutdown received...")
				if err := backend.Disconnect(ctx, true); err != nil {
					log.Printf("warning: backend disconnect error: %v\n", err)
				}
				cancel()
				return

			case syscall.SIGTSTP:
				// Toggle paused
				localscribe.TogglePaused()
				if localscribe.IsPaused() {
					fmt.Print("PAUSED\r")
				} else {
					// Clear out the text
					fmt.Print("      \r")
				}
			}
		}
	}()

	// Start optional logging goroutines unless transcript-only mode is enabled
	if !cfg.TranscriptOnly {
		go localscribe.PollZoomStatus(cfg)
		go localscribe.PollChromeHistory(cfg)
		localscribe.ScribeIPInfo(cfg)
	}

	// Always start REST server for metadata API
	go launchRESTServer(cfg)

	log.Println("connecting to transcription backend")
	if err := backend.Connect(ctx); err != nil {
		log.Fatalf("connect to backend failed: %v\n", err)
	}

	log.Println("Press Ctrl+Z to pause/resume, Ctrl+C to quit.")

	if err := localscribe.StartTranscriptionLoop(ctx, backend, rec); err != nil {
		log.Fatalf("transcription loop error: %v\n", err)
	}

	log.Println("localscribe exit: OK")
}

func launchRESTServer(cfg localscribe.Config) {
	if err := localscribe.StartRESTServer(cfg); err != nil {
		log.Printf("web server exited with error: %v\n", err)
	}
}
