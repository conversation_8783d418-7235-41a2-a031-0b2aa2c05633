package localscribe

import (
	"database/sql"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"time"

	_ "github.com/mattn/go-sqlite3"
)

// PollChromeHistory monitors Chrome history for new entries and logs them
func PollChromeHistory(cfg Config) {
	log.Println("chrome history polling start")
	defer log.Println("chrome history polling end")

	ticker := time.NewTicker(30 * time.Second) // Check every 30 seconds
	defer ticker.Stop()

	var lastCheckTime time.Time = time.Now().Add(-1 * time.Hour) // Start with 1 hour ago

	for {
		select {
		case <-cfg.Context.Done():
			return
		case <-ticker.C:
			if err := checkChromeHistory(cfg, lastCheckTime); err != nil {
				log.Printf("Error checking Chrome history: %v", err)
			}
			lastCheckTime = time.Now()
		}
	}
}

func checkChromeHistory(cfg Config, since time.Time) error {
	// Chrome history database path
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return fmt.Errorf("failed to get home directory: %w", err)
	}

	historyPath := filepath.Join(homeDir, "Library", "Application Support", "Google", "Chrome", "Default", "History")

	// Check if the history file exists
	if _, err := os.Stat(historyPath); os.IsNotExist(err) {
		return fmt.Errorf("Chrome history file not found: %s", historyPath)
	}

	// Copy the history file to a temporary location to avoid locking issues
	tempPath := filepath.Join(os.TempDir(), fmt.Sprintf("chrome_history_%d.db", time.Now().Unix()))
	if err := copyFile(historyPath, tempPath); err != nil {
		return fmt.Errorf("failed to copy history file: %w", err)
	}
	defer os.Remove(tempPath)

	// Open the database
	db, err := sql.Open("sqlite3", tempPath)
	if err != nil {
		return fmt.Errorf("failed to open history database: %w", err)
	}
	defer db.Close()

	// Convert since time to Chrome's timestamp format (microseconds since 1601-01-01)
	chromeEpoch := time.Date(1601, 1, 1, 0, 0, 0, 0, time.UTC)
	sinceChrome := since.Sub(chromeEpoch).Microseconds()

	// Query for recent visits
	query := `
		SELECT urls.url, urls.title, datetime(visits.visit_time/1000000 + (strftime('%s', '1601-01-01')), 'unixepoch', 'localtime') as visited_local
		FROM visits 
		JOIN urls ON visits.url = urls.id 
		WHERE visits.visit_time > ? 
		ORDER BY visits.visit_time DESC 
		LIMIT 50
	`

	rows, err := db.Query(query, sinceChrome)
	if err != nil {
		return fmt.Errorf("failed to query history: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		var chromeURL, title, visitedLocal string
		if err := rows.Scan(&chromeURL, &title, &visitedLocal); err != nil {
			log.Printf("scan error: %v", err)
			continue
		}

		// Build the line for the localscribe log
		line := fmt.Sprintf("%s %s url: %s, title: %q, visited: %s", getDateTime(), "%%% google chrome", chromeURL, title, visitedLocal)
		if err := AtomicAppendToFile(cfg.LogFile, line); err != nil {
			log.Printf("failed to append line to log: %v", err)
		}
	}
	if err := rows.Err(); err != nil {
		return fmt.Errorf("rows iteration error: %w", err)
	}

	return nil
}

func copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()

	_, err = destFile.ReadFrom(sourceFile)
	return err
}
