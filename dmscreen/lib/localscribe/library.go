package localscribe

import (
	"context"
	"fmt"
	"log"
	"os"
	"path"
	"sync"
	"time"

	"github.com/AssemblyAI/assemblyai-go-sdk"
	"github.com/gordonklaus/portaudio"
)

// TranscriptionSession represents an active transcription session
type TranscriptionSession struct {
	ctx        context.Context
	cancel     context.CancelFunc
	config     LibraryConfig
	backend    TranscriptionBackend
	recorder   *Recorder
	running    bool
	mutex      sync.Mutex
	outputChan chan string // Channel for sending output to debug console
}

// OutputMessage represents a message to be sent to the debug console
type OutputMessage struct {
	Timestamp time.Time
	Source    string // "localscribe", "transcription", "error", etc.
	Message   string
	Level     string // "info", "warning", "error", "debug"
}

// LibraryConfig extends the basic Config with library-specific options
type LibraryConfig struct {
	Config
	OutputChannel chan OutputMessage // Channel to send debug messages
	LogToConsole  bool               // Whether to also log to console
}

// NewLibraryConfig creates a new library configuration with sensible defaults
func NewLibraryConfig(assemblyAIKey, logFile string, output<PERSON>han chan OutputMessage) LibraryConfig {
	if logFile == "" {
		defaultNowString := time.Now().Format("20060102_1504")
		logFile = path.Join(os.Getenv("HOME"), ".local", "scribe", "transcription-"+defaultNowString+".log")
	}

	return LibraryConfig{
		Config: Config{
			LogFile:         logFile,
			AssemblyAIKey:   assemblyAIKey,
			SampleRate:      16000,
			FramesPerBuffer: 3200,
			RESTPort:        8080,
			TranscriptOnly:  true, // Default to transcript-only for library usage
		},
		OutputChannel: outputChan,
		LogToConsole:  false, // Default to not logging to console in library mode
	}
}

// sendOutput sends a message to the debug console if channel is available
func (s *TranscriptionSession) sendOutput(source, message, level string) {
	if s.config.OutputChannel != nil {
		select {
		case s.config.OutputChannel <- OutputMessage{
			Timestamp: time.Now(),
			Source:    source,
			Message:   message,
			Level:     level,
		}:
		default:
			// Channel is full, skip message to avoid blocking
		}
	}

	// Also log to console if enabled
	if s.config.LogToConsole {
		log.Printf("[%s] %s: %s", level, source, message)
	}
}

// NewTranscriptionSession creates a new transcription session
func NewTranscriptionSession(config LibraryConfig) (*TranscriptionSession, error) {
	if config.AssemblyAIKey == "" {
		return nil, fmt.Errorf("AssemblyAI API key is required")
	}

	ctx, cancel := context.WithCancel(context.Background())
	config.Config.Context = ctx

	session := &TranscriptionSession{
		ctx:        ctx,
		cancel:     cancel,
		config:     config,
		outputChan: make(chan string, 100),
	}

	session.sendOutput("localscribe", "Transcription session created", "info")
	return session, nil
}

// Start begins the transcription session
func (s *TranscriptionSession) Start() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.running {
		return fmt.Errorf("transcription session is already running")
	}

	s.sendOutput("localscribe", "Starting transcription session", "info")

	// Initialize PortAudio
	s.sendOutput("localscribe", "Initializing PortAudio", "debug")
	if err := portaudio.Initialize(); err != nil {
		s.sendOutput("localscribe", fmt.Sprintf("PortAudio initialization failed: %v", err), "error")
		return fmt.Errorf("portaudio init failed: %w", err)
	}

	// Create recorder
	s.sendOutput("localscribe", fmt.Sprintf("Creating voice recorder: sampleRate=%d, framesPerBuffer=%d",
		s.config.SampleRate, s.config.FramesPerBuffer), "debug")

	recorder, err := NewRecorder(s.config.SampleRate, s.config.FramesPerBuffer)
	if err != nil {
		portaudio.Terminate()
		s.sendOutput("localscribe", fmt.Sprintf("Failed to create recorder: %v", err), "error")
		return fmt.Errorf("failed to create recorder: %w", err)
	}
	s.recorder = recorder

	// Create backend with custom callbacks for library usage
	s.backend = s.createBackendWithCallbacks()
	if s.backend == nil {
		portaudio.Terminate()
		s.sendOutput("localscribe", "Failed to create AssemblyAI backend", "error")
		return fmt.Errorf("failed to create AssemblyAI backend")
	}

	s.sendOutput("localscribe", fmt.Sprintf("Transcribing to file: %s", s.config.LogFile), "info")

	// Connect to backend
	s.sendOutput("localscribe", "Connecting to transcription backend", "debug")
	if err := s.backend.Connect(s.ctx); err != nil {
		portaudio.Terminate()
		s.sendOutput("localscribe", fmt.Sprintf("Backend connection failed: %v", err), "error")
		return fmt.Errorf("connect to backend failed: %w", err)
	}

	// Start optional logging goroutines unless transcript-only mode is enabled
	if !s.config.TranscriptOnly {
		s.sendOutput("localscribe", "Starting optional logging goroutines", "debug")
		go s.pollZoomStatus()
		go s.pollChromeHistory()
		s.scribeIPInfo()
	}

	// Start REST server for metadata API
	go s.startRESTServer()

	// Start transcription loop in a goroutine
	go s.runTranscriptionLoop()

	s.running = true
	s.sendOutput("localscribe", "Transcription session started successfully", "info")
	return nil
}

// Stop ends the transcription session
func (s *TranscriptionSession) Stop() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.running {
		return fmt.Errorf("transcription session is not running")
	}

	s.sendOutput("localscribe", "Stopping transcription session", "info")

	// Pause sending to avoid session-closed errors
	SetPaused(true)

	// Disconnect backend
	if s.backend != nil {
		s.sendOutput("localscribe", "Disconnecting from backend", "debug")
		if err := s.backend.Disconnect(s.ctx, true); err != nil {
			s.sendOutput("localscribe", fmt.Sprintf("Backend disconnect warning: %v", err), "warning")
		}
	}

	// Cancel context to stop all goroutines
	s.cancel()

	// Cleanup recorder
	if s.recorder != nil {
		cleanupRecorder(s.recorder)
	}

	// Terminate PortAudio
	portaudio.Terminate()

	s.running = false
	s.sendOutput("localscribe", "Transcription session stopped", "info")
	return nil
}

// IsRunning returns whether the transcription session is currently active
func (s *TranscriptionSession) IsRunning() bool {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	return s.running
}

// GetOutputChannel returns the channel for receiving transcription output
func (s *TranscriptionSession) GetOutputChannel() <-chan string {
	return s.outputChan
}

// TogglePause toggles the pause state of the transcription
func (s *TranscriptionSession) TogglePause() {
	TogglePaused()
	if IsPaused() {
		s.sendOutput("localscribe", "Transcription paused", "info")
	} else {
		s.sendOutput("localscribe", "Transcription resumed", "info")
	}
}

// GetLogFile returns the path to the current log file
func (s *TranscriptionSession) GetLogFile() string {
	return s.config.LogFile
}

// createBackendWithCallbacks creates an AssemblyAI backend with custom callbacks for library usage
func (s *TranscriptionSession) createBackendWithCallbacks() *AssemblyAIBackend {
	transcriber := &assemblyai.RealTimeTranscriber{
		OnSessionBegins: func(e assemblyai.SessionBegins) {
			s.sendOutput("transcription", "Transcription session begin", "info")
		},
		OnSessionTerminated: func(e assemblyai.SessionTerminated) {
			s.sendOutput("transcription", "Transcription session end", "info")
		},
		OnFinalTranscript: func(t assemblyai.FinalTranscript) {
			// Send transcript to output channel
			select {
			case s.outputChan <- t.Text:
			default:
				// Channel full, skip
			}

			// Log to file
			line := fmt.Sprintf("%s - %s", getDateTime(), t.Text)
			if err := AtomicAppendToFile(s.config.LogFile, line); err != nil {
				s.sendOutput("transcription", fmt.Sprintf("Failed to write to log file: %v", err), "error")
			}

			s.sendOutput("transcription", t.Text, "info")
		},
		OnPartialTranscript: func(t assemblyai.PartialTranscript) {
			// For library usage, we can send partial transcripts to debug console
			s.sendOutput("transcription", fmt.Sprintf("Partial: %s", t.Text), "debug")
		},
		OnError: func(err error) {
			s.sendOutput("transcription", fmt.Sprintf("AssemblyAI error: %v", err), "error")
		},
	}

	client := assemblyai.NewRealTimeClientWithOptions(
		assemblyai.WithRealTimeAPIKey(s.config.AssemblyAIKey),
		assemblyai.WithRealTimeTranscriber(transcriber),
		assemblyai.WithRealTimeSampleRate(s.config.SampleRate),
	)

	return &AssemblyAIBackend{
		client:      client,
		transcriber: transcriber,
	}
}

// runTranscriptionLoop runs the main transcription loop
func (s *TranscriptionSession) runTranscriptionLoop() {
	s.sendOutput("localscribe", "Starting transcription loop", "debug")

	if err := StartTranscriptionLoop(s.ctx, s.backend, s.recorder); err != nil {
		s.sendOutput("localscribe", fmt.Sprintf("Transcription loop error: %v", err), "error")
	}

	s.sendOutput("localscribe", "Transcription loop ended", "debug")
}

// pollZoomStatus polls Zoom status (wrapper for library usage)
func (s *TranscriptionSession) pollZoomStatus() {
	s.sendOutput("localscribe", "Starting Zoom status polling", "debug")
	PollZoomStatus(s.config.Config)
}

// pollChromeHistory polls Chrome history (wrapper for library usage)
func (s *TranscriptionSession) pollChromeHistory() {
	s.sendOutput("localscribe", "Starting Chrome history polling", "debug")
	PollChromeHistory(s.config.Config)
}

// scribeIPInfo logs IP information (wrapper for library usage)
func (s *TranscriptionSession) scribeIPInfo() {
	s.sendOutput("localscribe", "Logging IP information", "debug")
	ScribeIPInfo(s.config.Config)
}

// startRESTServer starts the REST server (wrapper for library usage)
func (s *TranscriptionSession) startRESTServer() {
	s.sendOutput("localscribe", fmt.Sprintf("Starting REST server on port %d", s.config.RESTPort), "debug")
	if err := StartRESTServer(s.config.Config); err != nil {
		s.sendOutput("localscribe", fmt.Sprintf("REST server error: %v", err), "error")
	}
}
