.PHONY: build clean install test

# Build the standalone localscribe binary
build:
	cd cmd/localscribe && go build -o ../../bin/localscribe .

# Install dependencies
deps:
	go mod download
	go mod tidy

# Clean build artifacts
clean:
	rm -rf bin/

# Install the binary to GOPATH/bin
install: build
	cp bin/localscribe $(GOPATH)/bin/

# Run tests
test:
	go test ./...

# Create bin directory
bin:
	mkdir -p bin

# Build with bin directory creation
build-with-dir: bin build

# Default target
all: deps build-with-dir
